import torch
import os
import sys

# Set path to the policy.pt file
model_path = "legged_gym/logs/rough_robs3go/exported/policies/policy.pt"
# model_path = "legged_gym/logs/rough_robs3go_detail/exported/policies/policy.pt"

# Create output directory if it doesn't exist
output_dir = os.path.dirname(model_path)
os.makedirs(output_dir, exist_ok=True)

# Load the TorchScript model
try:
    model = torch.jit.load(model_path)
    model.eval()
    print(f"Successfully loaded model from {model_path}")
    print(f"Model structure: {model}")
except Exception as e:
    print(f"Error loading model: {e}")
    sys.exit(1)

# Create example inputs
# Adjust these sizes based on your actual model requirements
encoder_input = torch.randn(1, 270)  # Batch size 1, 270 features for encoder
actor_input = torch.cat((torch.randn(1, 45), torch.randn(1, 3), torch.randn(1, 16)), dim=1)  # Combined input for actor

try:
    # First, trace the full model to understand the execution
    with torch.no_grad():
        output = model(encoder_input)
        print(f"Model output shape: {output.shape}")
    
    # Export the full model to ONNX
    torch.onnx.export(
        model,
        encoder_input,
        os.path.join(output_dir, "full_model.onnx"),
        export_params=True,
        opset_version=11,
        do_constant_folding=True,
        input_names=['input'],
        output_names=['output'],
        # dynamic_axes={'input': {0: 'batch_size'}, 'output': {0: 'batch_size'}}
    )
    print(f"Full model exported to {os.path.join(output_dir, 'full_model.onnx')}")
    
    # Try to access encoder and actor components
    if hasattr(model, 'encoder') and hasattr(model, 'actor'):
        # Export encoder
        torch.onnx.export(
            model.encoder,
            encoder_input,
            os.path.join(output_dir, "encoder.onnx"),
            export_params=True,
            opset_version=11,
            do_constant_folding=True,
            input_names=['input'],
            output_names=['output'],
            # dynamic_axes={'input': {0: 'batch_size'}, 'output': {0: 'batch_size'}}
        )
        print(f"Encoder exported to {os.path.join(output_dir, 'encoder.onnx')}")
        
        # Export actor
        # For the actor, we need to prepare the input as it would receive from the encoder
        with torch.no_grad():
            encoder_output = model.encoder(encoder_input)
            parts = encoder_output[:, 0:19]
            vel, z = parts[..., :3], parts[..., 3:]
            z = torch.nn.functional.normalize(z, dim=-1, p=2.0)
            actor_input = torch.cat((encoder_input[:, 0:45], vel, z), dim=1)
            
        torch.onnx.export(
            model.actor,
            actor_input,
            os.path.join(output_dir, "policy.onnx"),
            export_params=True,
            opset_version=11,
            do_constant_folding=True,
            input_names=['input'],
            output_names=['output'],
            # dynamic_axes={'input': {0: 'batch_size'}, 'output': {0: 'batch_size'}}
        )
        print(f"policy exported to {os.path.join(output_dir, 'policy.onnx')}")
    else:
        print("Model doesn't have separate encoder and policy attributes. Trying direct export of submodules...")

        # Try to export the estimator directly
        try:
            print("Attempting to export estimator directly...")
            torch.onnx.export(
                model.estimator,
                encoder_input,
                os.path.join(output_dir, "encoder.onnx"),
                export_params=True,
                opset_version=11,
                do_constant_folding=True,
                input_names=['input'],
                output_names=['output'],
                # dynamic_axes={'input': {0: 'batch_size'}, 'output': {0: 'batch_size'}}
            )
            print(f"Estimator exported directly to {os.path.join(output_dir, 'encoder.onnx')}")
        except Exception as e:
            print(f"Error exporting estimator directly: {e}")

            # Try alternative approach: create a simple function-based wrapper
            try:
                print("Trying function-based encoder wrapper...")

                def encoder_func(x):
                    with torch.no_grad():
                        parts = model.estimator(x)[:, 0:19]
                        return parts

                # Create a traced version of the function
                traced_encoder = torch.jit.trace(encoder_func, encoder_input)

                torch.onnx.export(
                    traced_encoder,
                    encoder_input,
                    os.path.join(output_dir, "encoder.onnx"),
                    export_params=True,
                    opset_version=11,
                    do_constant_folding=True,
                    input_names=['input'],
                    output_names=['output'],
                    # dynamic_axes={'input': {0: 'batch_size'}, 'output': {0: 'batch_size'}}
                )
                print(f"Function-based encoder exported to {os.path.join(output_dir, 'encoder.onnx')}")
            except Exception as e2:
                print(f"Error with function-based encoder: {e2}")

        # Try to export the actor directly
        try:
            print("Attempting to export policy directly...")
            # First, get the proper input format for the actor
            with torch.no_grad():
                parts = model.estimator(encoder_input)[:, 0:19]
                vel, z = parts[..., :3], parts[..., 3:]
                z = torch.nn.functional.normalize(z, dim=-1, p=2.0)
                actor_input = torch.cat((encoder_input[:, 0:45], vel, z), dim=1)

            torch.onnx.export(
                model.actor,
                actor_input,
                os.path.join(output_dir, "policy.onnx"),
                export_params=True,
                opset_version=11,
                do_constant_folding=True,
                input_names=['input'],
                output_names=['output'],
                # dynamic_axes={'input': {0: 'batch_size'}, 'output': {0: 'batch_size'}}
            )
            print(f"policy exported directly to {os.path.join(output_dir, 'policy.onnx')}")
        except Exception as e:
            print(f"Error exporting policy directly: {e}")

            # Try alternative approach: create a simple function-based wrapper
            try:
                print("Trying function-based policy wrapper...")

                def actor_func(x):
                    with torch.no_grad():
                        return model.actor(x)

                # Create a traced version of the function
                traced_actor = torch.jit.trace(actor_func, actor_input)

                torch.onnx.export(
                    traced_actor,
                    actor_input,
                    os.path.join(output_dir, "policy.onnx"),
                    export_params=True,
                    opset_version=11,
                    do_constant_folding=True,
                    input_names=['input'],
                    output_names=['output'],
                    # dynamic_axes={'input': {0: 'batch_size'}, 'output': {0: 'batch_size'}}
                )
                print(f"Function-based policy exported to {os.path.join(output_dir, 'policy.onnx')}")
            except Exception as e2:
                print(f"Error with function-based policy: {e2}")

except Exception as e:
    print(f"Error during export: {e}")
    import traceback
    traceback.print_exc()

# Verify the exported models
try:
    import onnx

    # Check full model
    full_model_path = os.path.join(output_dir, "full_model.onnx")
    if os.path.exists(full_model_path):
        full_model_onnx = onnx.load(full_model_path)
        onnx.checker.check_model(full_model_onnx)
        print("Full model ONNX is valid")
    else:
        print("Full model ONNX file not found")

    # Check encoder model
    encoder_path = os.path.join(output_dir, "encoder.onnx")
    if os.path.exists(encoder_path):
        encoder_onnx = onnx.load(encoder_path)
        onnx.checker.check_model(encoder_onnx)
        print("Encoder ONNX model is valid")
    else:
        print("Encoder ONNX file not found")

    # Check actor model
    policy_path = os.path.join(output_dir, "policy.onnx")
    if os.path.exists(policy_path):
        policy_onnx = onnx.load(policy_path)
        onnx.checker.check_model(policy_onnx)
        print("policy ONNX model is valid")
    else:
        print("policy ONNX file not found")
except ImportError:
    print("ONNX package not found. Install with 'pip install onnx' to verify models.")
except Exception as e:
    print(f"Error verifying ONNX models: {e}")
